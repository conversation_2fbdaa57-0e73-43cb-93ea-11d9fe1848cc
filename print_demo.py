#!/usr/bin/env python3
"""
Advanced Print Demo - A comprehensive demonstration of printing techniques in Python
"""
import sys
import time
import json
import pprint
import textwrap
import itertools
import threading
from datetime import datetime
from typing import Any, Dict, List, Union
from contextlib import contextmanager
from dataclasses import dataclass, field


class ColorCodes:
    """ANSI color codes for terminal output"""
    # Foreground colors
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"

    # Background colors
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"

    # Styles
    BOLD = "\033[1m"
    FAINT = "\033[2m"
    ITALIC = "\033[3m"
    UNDERLINE = "\033[4m"
    BLINK = "\033[5m"
    REVERSE = "\033[7m"
    STRIKE = "\033[9m"

    # Reset
    RESET = "\033[0m"


@dataclass
class Person:
    """Example dataclass for demonstration"""
    name: str
    age: int
    email: str
    skills: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __str__(self) -> str:
        return f"{self.name} ({self.age})"


def basic_printing():
    """Demonstrates basic print functionality with advanced techniques"""
    print("Hello, World!")

    # Print to stderr
    print("This is an error message", file=sys.stderr)

    # Print with flush (useful for real-time output)
    for i in range(5):
        print(f"Counting: {i}", end="\r", flush=True)
        time.sleep(0.3)
    print()  # New line after the counting

    # Print with different separator and end
    print("Words", "joined", "with", "custom", "separator", sep=" → ", end=" ◄\n")

    # Print raw strings (useful for regex or file paths)
    print(r"Raw string: C:\Users\<USER>\Documents")

    # Print using file objects
    with open("temp_output.txt", "w") as f:
        print("This text goes to a file instead of the console", file=f)

    # Read it back and print to console
    try:
        with open("temp_output.txt", "r") as f:
            print("From file:", f.read().strip())
    except FileNotFoundError:
        print("File not found")


def formatted_printing():
    """Demonstrates advanced formatted printing"""
    name = "Python"
    version = 3.11
    release_date = datetime.now()

    # Advanced f-string formatting
    print(f"{name} version {version:.1f} ({release_date:%Y-%m-%d})")

    # Number formatting
    print(f"Large number with commas: {1234567:,}")
    print(f"Percentage: {0.8563:.2%}")
    print(f"Scientific notation: {123456789:.2e}")

    # Padding and alignment with dynamic width
    width = 15
    print(f"{'Left':<{width}}|{'Center':^{width}}|{'Right':>{width}}")
    print(f"{'Dynamic':<{width}}|{'Width':^{width}}|{'Example':>{width}}")

    # Nested formatting expressions (Python 3.8+)
    precision = 3
    print(f"Pi with {precision} decimal places: {3.14159:.{precision}f}")

    # Format specifiers with flags
    print(f"Signed numbers: {42:+d}, {-42:+d}")
    print(f"Zero-padded: {42:05d}")

    # Datetime formatting
    now = datetime.now()
    print(f"Date formats: {now:%Y-%m-%d}, {now:%H:%M:%S}, {now:%A, %B %d, %Y}")

    # Format dictionary values
    data = {"name": "Alice", "score": 95.5}
    print(f"Name: {data['name']}, Score: {data['score']:.1f}")


def printing_data_structures():
    """Demonstrates printing various data structures with advanced techniques"""
    # Create complex nested data
    complex_data = {
        "users": [
            Person(name="Alice", age=30, email="<EMAIL>",
                  skills=["Python", "Data Science"],
                  metadata={"last_login": "2023-05-15", "active": True}),
            Person(name="Bob", age=25, email="<EMAIL>",
                  skills=["JavaScript", "React"],
                  metadata={"last_login": "2023-05-10", "active": False})
        ],
        "settings": {
            "theme": "dark",
            "notifications": True,
            "preferences": {"language": "en", "timezone": "UTC"}
        },
        "statistics": {
            "visits": [123, 456, 789, 321, 654],
            "conversion_rate": 0.0345,
            "growth": {
                "q1": 0.05,
                "q2": 0.07,
                "q3": -0.02,
                "q4": 0.09
            }
        }
    }

    # Standard print
    print("\nStandard print:")
    print(complex_data)

    # Pretty print with custom settings
    print("\nPretty print with custom settings:")
    pp = pprint.PrettyPrinter(indent=2, width=80, depth=3, compact=False)
    pp.pprint(complex_data)

    # JSON formatting
    print("\nJSON formatting (indented):")
    print(json.dumps(complex_data, default=lambda o: o.__dict__, indent=2))

    # Table-like printing
    print("\nTable-like printing:")
    headers = ["Name", "Age", "Email", "Skills"]
    print(f"{headers[0]:<15} {headers[1]:<5} {headers[2]:<25} {headers[3]}")
    print("-" * 70)
    for user in complex_data["users"]:
        skills_str = ", ".join(user.skills)
        print(f"{user.name:<15} {user.age:<5} {user.email:<25} {skills_str}")

    # Printing with textwrap for long text
    long_text = "This is a very long text that needs to be wrapped to fit within a certain width. " * 3
    print("\nWrapped text:")
    print(textwrap.fill(long_text, width=60))


def colored_text_advanced():
    """Demonstrates advanced colored text and styling"""
    c = ColorCodes  # Shorthand

    # Gradient text
    print("\nGradient text:")
    colors = [c.RED, c.YELLOW, c.GREEN, c.CYAN, c.BLUE, c.MAGENTA]
    text = "Rainbow Text Example"
    gradient = ""
    for i, char in enumerate(text):
        color = colors[i % len(colors)]
        gradient += f"{color}{char}{c.RESET}"
    print(gradient)

    # Background colors
    print(f"\n{c.BLACK}{c.BG_WHITE}Black on white{c.RESET} {c.WHITE}{c.BG_BLUE}White on blue{c.RESET}")

    # Combined styles
    print(f"\n{c.BOLD}{c.UNDERLINE}{c.BRIGHT_GREEN}Bold, underlined bright green{c.RESET}")

    # Box drawing
    print("\nStyled box:")
    print(f"{c.YELLOW}┌{'─' * 30}┐{c.RESET}")
    print(f"{c.YELLOW}│{c.RESET} {c.BOLD}{c.BRIGHT_CYAN}Title in a box{c.RESET}{' ' * 16}{c.YELLOW}│{c.RESET}")
    print(f"{c.YELLOW}└{'─' * 30}┘{c.RESET}")

    # Progress bar
    print("\nColored progress bar:")
    total = 50
    for i in range(total + 1):
        progress = i / total
        bar_length = 40
        filled_length = int(bar_length * progress)
        bar = f"{c.BRIGHT_GREEN}{'█' * filled_length}{c.BRIGHT_RED}{'░' * (bar_length - filled_length)}{c.RESET}"
        percent = f"{c.BRIGHT_WHITE}{progress * 100:.1f}%{c.RESET}"
        print(f"\r[{bar}] {percent}", end="", flush=True)
        time.sleep(0.02)
    print()


@contextmanager
def timer_context():
    """Context manager to time and print execution time"""
    start_time = time.time()
    print(f"{ColorCodes.YELLOW}Operation started at {datetime.now().strftime('%H:%M:%S.%f')[:-3]}{ColorCodes.RESET}")
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        print(f"{ColorCodes.GREEN}Operation completed in {duration:.4f} seconds{ColorCodes.RESET}")


def advanced_printing_techniques():
    """Demonstrates more advanced printing techniques"""
    # Context manager for timing and printing
    with timer_context():
        print("Performing operation...")
        time.sleep(1.5)  # Simulate work

    # Animated spinner
    print("\nProcessing with spinner: ", end="", flush=True)
    spinner = itertools.cycle(['-', '/', '|', '\\'])
    for _ in range(20):
        print(f"\r{ColorCodes.CYAN}Processing... {next(spinner)}{ColorCodes.RESET}", end="", flush=True)
        time.sleep(0.1)
    print(f"\r{ColorCodes.GREEN}Processing... Done!{' ' * 5}{ColorCodes.RESET}")

    # Multi-line string with dedent
    code_example = textwrap.dedent("""
        def example_function():
            '''This is a docstring'''
            for i in range(10):
                # This is properly indented
                print(f"Value: {i}")
    """).strip()
    print("\nFormatted code example:")
    print(code_example)

    # Conditional printing based on verbosity level
    verbosity = 2  # 0=quiet, 1=normal, 2=verbose

    def log(message, level=1):
        if verbosity >= level:
            prefix = {0: "[ERROR] ", 1: "[INFO] ", 2: "[DEBUG] "}
            color = {0: ColorCodes.RED, 1: ColorCodes.RESET, 2: ColorCodes.BLUE}
            print(f"{color[level]}{prefix[level]}{message}{ColorCodes.RESET}")

    log("Critical error message", 0)
    log("Standard information", 1)
    log("Verbose debug details", 2)


def interactive_printing():
    """Demonstrates interactive printing techniques"""
    # Simple loading animation
    def loading_animation(duration=3):
        end_time = time.time() + duration
        print("Loading: ", end="", flush=True)

        while time.time() < end_time:
            for char in "⣾⣽⣻⢿⡿⣟⣯⣷":
                print(f"\r{ColorCodes.CYAN}Loading: {char}{ColorCodes.RESET}", end="", flush=True)
                time.sleep(0.1)
                if time.time() >= end_time:
                    break

        print(f"\r{ColorCodes.GREEN}Loading: Completed!{' ' * 10}{ColorCodes.RESET}")

    loading_animation(2)

    # Progress bar with percentage and ETA
    def progress_bar(iterable, prefix="", size=50, file=sys.stdout):
        count = len(iterable)
        start_time = time.time()

        def show(j):
            x = int(size * j / count)
            remaining_items = count - j
            if j > 0:
                elapsed = time.time() - start_time
                items_per_second = j / elapsed
                eta_seconds = remaining_items / items_per_second if items_per_second > 0 else 0
                eta = f"ETA: {int(eta_seconds)}s" if eta_seconds < 60 else f"ETA: {int(eta_seconds/60)}m {int(eta_seconds%60)}s"
            else:
                eta = "ETA: --"

            bar = f"{ColorCodes.BRIGHT_GREEN}{'█' * x}{ColorCodes.BRIGHT_RED}{'░' * (size - x)}{ColorCodes.RESET}"
            percent = f"{ColorCodes.BRIGHT_WHITE}{100 * j / count:.1f}%{ColorCodes.RESET}"
            file.write(f"\r{prefix}[{bar}] {j}/{count} {percent} {eta}")
            file.flush()

        show(0)
        for i, item in enumerate(iterable):
            yield item
            show(i + 1)
        file.write("\n")
        file.flush()

    print("\nProcessing items with progress bar:")
    for item in progress_bar(range(100), "Processing: ", 40):
        time.sleep(0.05)  # Simulate work

    # Two-line updating display
    print("\nReal-time updating display:")
    for i in range(10):
        status = ["Processing", "Analyzing", "Computing", "Evaluating"][i % 4]
        detail = f"Step {i+1}/10: {status} data batch {i}"
        print(f"\r\033[K{ColorCodes.YELLOW}{status}...{ColorCodes.RESET}", end="", flush=True)
        print(f"\r\033[1A\033[K{ColorCodes.CYAN}{detail}{ColorCodes.RESET}", end="\n\n", flush=True)
        time.sleep(0.5)

    print(f"{ColorCodes.GREEN}All processing completed!{ColorCodes.RESET}")


def main():
    """Main function to run all demonstrations"""
    c = ColorCodes

    # Title
    title = "ADVANCED PYTHON PRINTING TECHNIQUES"
    print(f"\n{c.BOLD}{c.BRIGHT_CYAN}{title.center(60, '=')}{c.RESET}\n")

    sections = [
        ("BASIC PRINTING", basic_printing),
        ("FORMATTED PRINTING", formatted_printing),
        ("PRINTING DATA STRUCTURES", printing_data_structures),
        ("COLORED TEXT ADVANCED", colored_text_advanced),
        ("ADVANCED TECHNIQUES", advanced_printing_techniques),
        ("INTERACTIVE PRINTING", interactive_printing)
    ]

    for title, func in sections:
        print(f"\n{c.BOLD}{c.BRIGHT_YELLOW}{'=' * 10} {title} {'=' * 10}{c.RESET}\n")
        func()
        time.sleep(0.5)  # Pause between sections

    print(f"\n{c.BOLD}{c.BRIGHT_GREEN}{'=' * 20} DEMO COMPLETE {'=' * 20}{c.RESET}\n")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n{ColorCodes.RED}Demo interrupted by user{ColorCodes.RESET}")
    finally:
        # Clean up any temporary files
        import os
        if os.path.exists("temp_output.txt"):
            os.remove("temp_output.txt")
