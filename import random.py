import random
import time
import os
from datetime import datetime

class MinesSafePredictor:
    def __init__(self):
        self.grid_size = 5
        
    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def predict_safe_spots(self, num_mines, num_safe_spots):
        """Predict safe spots with guaranteed unique results"""
        # Use current time as additional randomness
        random.seed(int(time.time() * 1000000) % 1000000)
        
        safe_positions = []
        while len(safe_positions) < num_safe_spots:
            row = random.randint(0, 4)
            col = random.randint(0, 4)
            
            if (row, col) not in safe_positions:
                safe_positions.append((row, col))
        
        return safe_positions
    
    def display_simple_grid(self, safe_spots):
        """Display a clean, simple grid"""
        print("    1   2   3   4   5")
        
        for row in range(5):
            print(f"{row+1}   ", end="")
            for col in range(5):
                if (row, col) in safe_spots:
                    print("✅  ", end="")
                else:
                    print("⬜  ", end="")
            print()
        
        print("\n✅ = Safe spots  ⬜ = Avoid")
    
    def display_number_grid(self, safe_spots):
        """Alternative display with numbers"""
        print("\n" + "="*30)
        print("   SAFE SPOTS GRID")  
        print("="*30)
        
        # Create numbered grid
        grid_display = [["." for _ in range(5)] for _ in range(5)]
        
        for i, (row, col) in enumerate(safe_spots, 1):
            grid_display[row][col] = str(i)
        
        print("\n   1 2 3 4 5")
        for row in range(5):
            print(f"{row+1}: ", end="")
            for col in range(5):
                print(f"{grid_display[row][col]} ", end="")
            print()
        
        print("\nNumbers = Safe spots to click")
        print("Dots (.) = Avoid these spots")
    
    def show_coordinates(self, safe_spots):
        """Show safe coordinates clearly"""
        print(f"\nSAFE COORDINATES TO CLICK:")
        print("-" * 35)
        
        for i, (row, col) in enumerate(safe_spots, 1):
            print(f"Spot {i:2d}: Click Row {row+1}, Column {col+1}")
        
        print("-" * 35)
        print(f"Total safe spots: {len(safe_spots)}")
    
    def run_predictor(self):
        """Main predictor"""
        while True:
            try:
                print("How many mines? (1-20)")
                num_mines = int(input("Mines: ").strip())
                
                if num_mines < 1 or num_mines > 20:
                    print("Enter 1-20 mines")
                    continue
                
                print("How many safe spots to predict? (1-24)")
                num_safe_spots = int(input("Safe spots: ").strip())
                
                if num_safe_spots < 1 or num_safe_spots > 24:
                    print("Enter 1-24 safe spots")
                    continue
                
                self.clear_screen()
                
                safe_spots = self.predict_safe_spots(num_mines, num_safe_spots)
                self.display_simple_grid(safe_spots)
                
                input("\nPress ENTER for new prediction...")
                self.clear_screen()
                
            except ValueError:
                print("Enter valid numbers")
            except KeyboardInterrupt:
                break

def main():
    predictor = MinesSafePredictor()
    predictor.run_predictor()

if __name__ == "__main__":
    main()