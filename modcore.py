import discord
from discord import app_commands
import asyncio
import datetime
import random
import json
import os
import aiohttp
import platform
import psutil
import time
import re
import typing

intents = discord.Intents.all()
client = discord.Client(intents=intents)
tree = app_commands.CommandTree(client)

@client.event
async def on_ready():
    print(f'Logged in as {client.user.name}')
    await client.change_presence(activity=discord.Activity(type=discord.ActivityType.watching, name="ModCore"))
    try:
        synced = await tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(e)

async def send_error_embed(interaction, error_message):
    embed = discord.Embed(
        title="Error",
        description=error_message,
        color=discord.Color.red()
    )
    if interaction.response.is_done():
        await interaction.followup.send(embed=embed, ephemeral=True)
    else:
        await interaction.response.send_message(embed=embed, ephemeral=True)

class Moderation:
    def __init__(self):
        self.warns = {}
        self.load_warns()

    def load_warns(self):
        if os.path.exists('warns.json'):
            with open('warns.json', 'r') as f:
                self.warns = json.load(f)

    def save_warns(self):
        with open('warns.json', 'w') as f:
            json.dump(self.warns, f)

moderation = Moderation()

@tree.command(name="kick", description="Kick a member from the server")
@app_commands.default_permissions(kick_members=True)
async def kick(interaction: discord.Interaction, member: discord.Member, reason: str = None):
    if not interaction.user.guild_permissions.kick_members:
        await send_error_embed(interaction, "You don't have permission to use this command")
        return

    try:
        await member.kick(reason=reason)

        embed = discord.Embed(
            title="Member Kicked",
            description=f"{member.mention} has been kicked",
            color=discord.Color.orange(),
            timestamp=datetime.datetime.now()
        )
        embed.add_field(name="Reason", value=reason or "No reason provided")
        embed.set_thumbnail(url=member.avatar.url if member.avatar else member.default_avatar.url)
        embed.set_footer(text=f"Kicked by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)

        await interaction.response.send_message(embed=embed)
    except discord.Forbidden:
        await send_error_embed(interaction, "I don't have permission to kick this member")
    except Exception as e:
        await send_error_embed(interaction, f"An error occurred: {e}")


@tree.command(name="ban", description="Ban a member from the server")
@app_commands.default_permissions(ban_members=True)
async def ban(interaction: discord.Interaction, member: discord.Member, reason: str = None):
    if not interaction.user.guild_permissions.ban_members:
        await send_error_embed(interaction, "You don't have permission to use this command")
        return

    try:
        await member.ban(reason=reason)

        embed = discord.Embed(
            title="Member Banned",
            description=f"{member.mention} has been banned",
            color=discord.Color.red(),
            timestamp=datetime.datetime.now()
        )
        embed.add_field(name="Reason", value=reason or "No reason provided")
        embed.set_thumbnail(url=member.avatar.url if member.avatar else member.default_avatar.url)
        embed.set_footer(text=f"Banned by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)

        await interaction.response.send_message(embed=embed)
    except discord.Forbidden:
        await send_error_embed(interaction, "I don't have permission to ban this member")
    except Exception as e:
        await send_error_embed(interaction, f"An error occurred: {e}")

@tree.command(name="unban", description="Unban a user from the server")
@app_commands.default_permissions(ban_members=True)
async def unban(interaction: discord.Interaction, user_id: str):
    if not interaction.user.guild_permissions.ban_members:
        await send_error_embed(interaction, "You don't have permission to use this command")
        return

    try:
        banned_users = [entry async for entry in interaction.guild.bans()]
        user_found = False

        for ban_entry in banned_users:
            user = ban_entry.user
            if str(user.id) == user_id or user.name.lower() == user_id.lower():
                await interaction.guild.unban(user)
                embed = discord.Embed(
                    title="Member Unbanned",
                    description=f"{user.mention} has been unbanned",
                    color=discord.Color.green(),
                    timestamp=datetime.datetime.now()
                )
                embed.set_footer(text=f"Unbanned by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)
                await interaction.response.send_message(embed=embed)
                user_found = True
                break

        if not user_found:
            await send_error_embed(interaction, "User not found in ban list")
    except discord.Forbidden:
        await send_error_embed(interaction, "I don't have permission to unban users")
    except Exception as e:
        await send_error_embed(interaction, f"An error occurred: {e}")

@tree.command(name="clear", description="Clear a specified number of messages from the channel")
@app_commands.default_permissions(manage_messages=True)
async def clear(interaction: discord.Interaction, amount: int):
    if not interaction.user.guild_permissions.manage_messages:
        await send_error_embed(interaction, "You don't have permission to use this command")
        return

    if amount < 1 or amount > 100:
        await send_error_embed(interaction, "Please provide a number between 1 and 100")
        return

    try:
        await interaction.response.defer(ephemeral=True)
        deleted = await interaction.channel.purge(limit=amount)

        embed = discord.Embed(
            title="Messages Cleared",
            description=f"{len(deleted)} messages have been cleared",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )
        embed.set_footer(text=f"Cleared by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)

        await interaction.followup.send(embed=embed, ephemeral=True)
    except discord.Forbidden:
        await send_error_embed(interaction, "I don't have permission to delete messages")
    except Exception as e:
        await send_error_embed(interaction, f"An error occurred: {e}")

@tree.command(name="ping", description="Check the bot's latency")
async def ping(interaction: discord.Interaction):
    start_time = time.time()
    await interaction.response.defer()
    end_time = time.time()

    latency = round(client.latency * 1000)
    api_latency = round((end_time - start_time) * 1000)

    embed = discord.Embed(
        title="🏓 Pong!",
        color=discord.Color.blue(),
        timestamp=datetime.datetime.now()
    )
    embed.add_field(name="Bot Latency", value=f"{latency}ms", inline=True)
    embed.add_field(name="API Latency", value=f"{api_latency}ms", inline=True)
    embed.set_footer(text=f"Requested by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)

    await interaction.followup.send(embed=embed)

@tree.command(name="serverinfo", description="Get information about the server")
async def serverinfo(interaction: discord.Interaction):
    guild = interaction.guild

    total_members = guild.member_count
    online_members = len([m for m in guild.members if m.status != discord.Status.offline])
    text_channels = len(guild.text_channels)
    voice_channels = len(guild.voice_channels)
    categories = len(guild.categories)
    roles = len(guild.roles)
    emojis = len(guild.emojis)

    created_at = guild.created_at.strftime("%Y-%m-%d %H:%M:%S")
    created_days_ago = (datetime.datetime.now() - guild.created_at).days

    embed = discord.Embed(
        title=f"{guild.name} Server Information",
        description=guild.description or "No description",
        color=discord.Color.blue(),
        timestamp=datetime.datetime.now()
    )

    if guild.icon:
        embed.set_thumbnail(url=guild.icon.url)

    embed.add_field(name="Owner", value=guild.owner.mention, inline=True)
    embed.add_field(name="Server ID", value=guild.id, inline=True)
    embed.add_field(name="Region", value=str(guild.region).capitalize() if hasattr(guild, 'region') else "Unknown", inline=True)

    embed.add_field(name="Members", value=f"Total: {total_members}\nOnline: {online_members}", inline=True)
    embed.add_field(name="Channels", value=f"Text: {text_channels}\nVoice: {voice_channels}\nCategories: {categories}", inline=True)
    embed.add_field(name="Other", value=f"Roles: {roles}\nEmojis: {emojis}", inline=True)

    embed.add_field(name="Created", value=f"{created_at}\n{created_days_ago} days ago", inline=False)

    if guild.premium_subscription_count:
        embed.add_field(name="Boost Status", value=f"Level {guild.premium_tier}\n{guild.premium_subscription_count} boosts", inline=False)

    embed.set_footer(text=f"Requested by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)

    await interaction.response.send_message(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def warn(self, ctx, member: discord.Member, *, reason=None):
        guild_id = str(ctx.guild.id)
        if guild_id not in self.warns:
            self.warns[guild_id] = {}

        user_id = str(member.id)
        if user_id not in self.warns[guild_id]:
            self.warns[guild_id][user_id] = []

        warn_data = {
            "reason": reason or "No reason provided",
            "timestamp": datetime.datetime.now().isoformat(),
            "moderator": ctx.author.id
        }

        self.warns[guild_id][user_id].append(warn_data)
        self.save_warns()

        embed = discord.Embed(
            title="Member Warned",
            description=f"{member.mention} has been warned",
            color=discord.Color.gold(),
            timestamp=datetime.datetime.now()
        )
        embed.add_field(name="Reason", value=reason or "No reason provided")
        embed.add_field(name="Warn Count", value=len(self.warns[guild_id][user_id]))
        embed.set_thumbnail(url=member.avatar.url if member.avatar else member.default_avatar.url)
        embed.set_footer(text=f"Warned by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def warnings(self, ctx, member: discord.Member):
        guild_id = str(ctx.guild.id)
        user_id = str(member.id)

        if guild_id not in self.warns or user_id not in self.warns[guild_id] or not self.warns[guild_id][user_id]:
            embed = discord.Embed(
                title="Warnings",
                description=f"{member.mention} has no warnings",
                color=discord.Color.green()
            )
            await ctx.send(embed=embed)
            return

        embed = discord.Embed(
            title=f"Warnings for {member}",
            color=discord.Color.gold(),
            timestamp=datetime.datetime.now()
        )

        for i, warn in enumerate(self.warns[guild_id][user_id], 1):
            moderator = ctx.guild.get_member(warn["moderator"])
            moderator_name = moderator.name if moderator else "Unknown Moderator"
            timestamp = datetime.datetime.fromisoformat(warn["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
            embed.add_field(
                name=f"Warning {i}",
                value=f"**Reason:** {warn['reason']}\n**By:** {moderator_name}\n**When:** {timestamp}",
                inline=False
            )

        embed.set_thumbnail(url=member.avatar.url if member.avatar else member.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def clearwarns(self, ctx, member: discord.Member):
        guild_id = str(ctx.guild.id)
        user_id = str(member.id)

        if guild_id in self.warns and user_id in self.warns[guild_id]:
            self.warns[guild_id][user_id] = []
            self.save_warns()

        embed = discord.Embed(
            title="Warnings Cleared",
            description=f"All warnings for {member.mention} have been cleared",
            color=discord.Color.green(),
            timestamp=datetime.datetime.now()
        )
        embed.set_footer(text=f"Cleared by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_roles=True)
    async def mute(self, ctx, member: discord.Member, *, reason=None):
        muted_role = discord.utils.get(ctx.guild.roles, name="Muted")
        if not muted_role:
            muted_role = await ctx.guild.create_role(name="Muted")

            for channel in ctx.guild.channels:
                await channel.set_permissions(muted_role, speak=False, send_messages=False, add_reactions=False)

        await member.add_roles(muted_role, reason=reason)

        embed = discord.Embed(
            title="Member Muted",
            description=f"{member.mention} has been muted",
            color=discord.Color.dark_orange(),
            timestamp=datetime.datetime.now()
        )
        embed.add_field(name="Reason", value=reason or "No reason provided")
        embed.set_thumbnail(url=member.avatar.url if member.avatar else member.default_avatar.url)
        embed.set_footer(text=f"Muted by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_roles=True)
    async def unmute(self, ctx, member: discord.Member):
        muted_role = discord.utils.get(ctx.guild.roles, name="Muted")
        if not muted_role:
            embed = discord.Embed(
                title="Error",
                description="Muted role not found",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        if muted_role not in member.roles:
            embed = discord.Embed(
                title="Error",
                description=f"{member.mention} is not muted",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        await member.remove_roles(muted_role)

        embed = discord.Embed(
            title="Member Unmuted",
            description=f"{member.mention} has been unmuted",
            color=discord.Color.green(),
            timestamp=datetime.datetime.now()
        )
        embed.set_thumbnail(url=member.avatar.url if member.avatar else member.default_avatar.url)
        embed.set_footer(text=f"Unmuted by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def slowmode(self, ctx, seconds: int):
        if seconds < 0 or seconds > 21600:
            embed = discord.Embed(
                title="Error",
                description="Slowmode delay must be between 0 and 21600 seconds (6 hours)",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        await ctx.channel.edit(slowmode_delay=seconds)

        if seconds == 0:
            embed = discord.Embed(
                title="Slowmode Disabled",
                description=f"Slowmode has been disabled in {ctx.channel.mention}",
                color=discord.Color.green(),
                timestamp=datetime.datetime.now()
            )
        else:
            embed = discord.Embed(
                title="Slowmode Enabled",
                description=f"Slowmode has been set to {seconds} seconds in {ctx.channel.mention}",
                color=discord.Color.blue(),
                timestamp=datetime.datetime.now()
            )

        embed.set_footer(text=f"Set by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_channels=True)
    async def lock(self, ctx, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        overwrites = channel.overwrites_for(ctx.guild.default_role)

        if overwrites.send_messages is False:
            embed = discord.Embed(
                title="Error",
                description=f"{channel.mention} is already locked",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        overwrites.send_messages = False
        await channel.set_permissions(ctx.guild.default_role, overwrite=overwrites)

        embed = discord.Embed(
            title="Channel Locked",
            description=f"{channel.mention} has been locked",
            color=discord.Color.red(),
            timestamp=datetime.datetime.now()
        )

        embed.set_footer(text=f"Locked by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_channels=True)
    async def unlock(self, ctx, channel: discord.TextChannel = None):
        channel = channel or ctx.channel
        overwrites = channel.overwrites_for(ctx.guild.default_role)

        if overwrites.send_messages is not False:
            embed = discord.Embed(
                title="Error",
                description=f"{channel.mention} is not locked",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        overwrites.send_messages = None
        await channel.set_permissions(ctx.guild.default_role, overwrite=overwrites)

        embed = discord.Embed(
            title="Channel Unlocked",
            description=f"{channel.mention} has been unlocked",
            color=discord.Color.green(),
            timestamp=datetime.datetime.now()
        )

        embed.set_footer(text=f"Unlocked by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def nuke(self, ctx, channel: discord.TextChannel = None):
        channel = channel or ctx.channel

        embed = discord.Embed(
            title="Channel Nuke Confirmation",
            description=f"Are you sure you want to nuke {channel.mention}? This will delete the channel and create a new one with the same settings.\n\nReact with ✅ to confirm or ❌ to cancel.",
            color=discord.Color.red(),
            timestamp=datetime.datetime.now()
        )

        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        confirmation_msg = await ctx.send(embed=embed)
        await confirmation_msg.add_reaction("✅")
        await confirmation_msg.add_reaction("❌")

        def check(reaction, user):
            return user == ctx.author and str(reaction.emoji) in ["✅", "❌"] and reaction.message.id == confirmation_msg.id

        try:
            reaction, user = await self.bot.wait_for("reaction_add", timeout=60.0, check=check)

            if str(reaction.emoji) == "✅":
                position = channel.position
                category = channel.category
                overwrites = channel.overwrites

                new_channel = await channel.clone(reason=f"Channel nuked by {ctx.author}")
                await channel.delete(reason=f"Channel nuked by {ctx.author}")

                await new_channel.edit(position=position, category=category)

                nuke_embed = discord.Embed(
                    title="Channel Nuked",
                    description=f"This channel has been nuked by {ctx.author.mention}",
                    color=discord.Color.red(),
                    timestamp=datetime.datetime.now()
                )

                nuke_embed.set_image(url="https://media.giphy.com/media/HhTXt43pk1I1W/giphy.gif")

                await new_channel.send(embed=nuke_embed)
            else:
                cancel_embed = discord.Embed(
                    title="Nuke Cancelled",
                    description="Channel nuke has been cancelled",
                    color=discord.Color.green(),
                    timestamp=datetime.datetime.now()
                )

                await confirmation_msg.edit(embed=cancel_embed)
                await confirmation_msg.clear_reactions()
        except asyncio.TimeoutError:
            timeout_embed = discord.Embed(
                title="Nuke Cancelled",
                description="Channel nuke has been cancelled due to timeout",
                color=discord.Color.green(),
                timestamp=datetime.datetime.now()
            )

            await confirmation_msg.edit(embed=timeout_embed)
            await confirmation_msg.clear_reactions()

    @commands.command()
    @commands.has_permissions(manage_roles=True)
    async def role(self, ctx, member: discord.Member, *, role: discord.Role):
        if role in member.roles:
            await member.remove_roles(role)
            action = "removed from"
            color = discord.Color.red()
        else:
            await member.add_roles(role)
            action = "added to"
            color = discord.Color.green()

        embed = discord.Embed(
            title="Role Updated",
            description=f"{role.mention} has been {action} {member.mention}",
            color=color,
            timestamp=datetime.datetime.now()
        )

        embed.set_footer(text=f"Updated by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_nicknames=True)
    async def nick(self, ctx, member: discord.Member, *, nickname=None):
        old_nick = member.nick or member.name

        await member.edit(nick=nickname)

        if nickname:
            embed = discord.Embed(
                title="Nickname Changed",
                description=f"{member.mention}'s nickname has been changed",
                color=discord.Color.blue(),
                timestamp=datetime.datetime.now()
            )

            embed.add_field(name="Old Nickname", value=old_nick, inline=True)
            embed.add_field(name="New Nickname", value=nickname, inline=True)
        else:
            embed = discord.Embed(
                title="Nickname Reset",
                description=f"{member.mention}'s nickname has been reset",
                color=discord.Color.blue(),
                timestamp=datetime.datetime.now()
            )

            embed.add_field(name="Old Nickname", value=old_nick, inline=True)

        embed.set_footer(text=f"Changed by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)
        await ctx.send(embed=embed)

class Utility(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.start_time = time.time()
        self.afk_users = {}

    @commands.command()
    async def ping(self, ctx):
        start_time = time.time()
        message = await ctx.send("Pinging...")
        end_time = time.time()

        latency = round(self.bot.latency * 1000)
        api_latency = round((end_time - start_time) * 1000)

        embed = discord.Embed(
            title="🏓 Pong!",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )
        embed.add_field(name="Bot Latency", value=f"{latency}ms", inline=True)
        embed.add_field(name="API Latency", value=f"{api_latency}ms", inline=True)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await message.edit(content=None, embed=embed)

    @commands.command()
    async def serverinfo(self, ctx):
        guild = ctx.guild

        total_members = guild.member_count
        online_members = len([m for m in guild.members if m.status != discord.Status.offline])
        text_channels = len(guild.text_channels)
        voice_channels = len(guild.voice_channels)
        categories = len(guild.categories)
        roles = len(guild.roles)
        emojis = len(guild.emojis)

        created_at = guild.created_at.strftime("%Y-%m-%d %H:%M:%S")
        created_days_ago = (datetime.datetime.now() - guild.created_at).days

        embed = discord.Embed(
            title=f"{guild.name} Server Information",
            description=guild.description or "No description",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        if guild.icon:
            embed.set_thumbnail(url=guild.icon.url)

        embed.add_field(name="Owner", value=guild.owner.mention, inline=True)
        embed.add_field(name="Server ID", value=guild.id, inline=True)
        embed.add_field(name="Region", value=str(guild.region).capitalize() if hasattr(guild, 'region') else "Unknown", inline=True)

        embed.add_field(name="Members", value=f"Total: {total_members}\nOnline: {online_members}", inline=True)
        embed.add_field(name="Channels", value=f"Text: {text_channels}\nVoice: {voice_channels}\nCategories: {categories}", inline=True)
        embed.add_field(name="Other", value=f"Roles: {roles}\nEmojis: {emojis}", inline=True)

        embed.add_field(name="Created", value=f"{created_at}\n{created_days_ago} days ago", inline=False)

        if guild.premium_subscription_count:
            embed.add_field(name="Boost Status", value=f"Level {guild.premium_tier}\n{guild.premium_subscription_count} boosts", inline=False)

        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def userinfo(self, ctx, member: discord.Member = None):
        member = member or ctx.author

        roles = [role.mention for role in member.roles if role.name != "@everyone"]
        roles_str = ", ".join(roles) if roles else "None"

        joined_at = member.joined_at.strftime("%Y-%m-%d %H:%M:%S")
        joined_days_ago = (datetime.datetime.now() - member.joined_at).days

        created_at = member.created_at.strftime("%Y-%m-%d %H:%M:%S")
        created_days_ago = (datetime.datetime.now() - member.created_at).days

        embed = discord.Embed(
            title=f"{member} User Information",
            color=member.color,
            timestamp=datetime.datetime.now()
        )

        embed.set_thumbnail(url=member.avatar.url if member.avatar else member.default_avatar.url)

        embed.add_field(name="ID", value=member.id, inline=True)
        embed.add_field(name="Nickname", value=member.nick or "None", inline=True)
        embed.add_field(name="Status", value=str(member.status).capitalize(), inline=True)

        embed.add_field(name="Joined Server", value=f"{joined_at}\n{joined_days_ago} days ago", inline=True)
        embed.add_field(name="Account Created", value=f"{created_at}\n{created_days_ago} days ago", inline=True)

        if member.activity:
            activity_type = str(member.activity.type).split('.')[-1].capitalize()
            activity_name = member.activity.name
            embed.add_field(name="Activity", value=f"{activity_type}: {activity_name}", inline=False)

        if roles:
            embed.add_field(name=f"Roles [{len(roles)}]", value=roles_str, inline=False)

        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def avatar(self, ctx, member: discord.Member = None):
        member = member or ctx.author

        embed = discord.Embed(
            title=f"{member}'s Avatar",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        avatar_url = member.avatar.url if member.avatar else member.default_avatar.url
        embed.set_image(url=avatar_url)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def botinfo(self, ctx):
        embed = discord.Embed(
            title="ModCore Information",
            description="A powerful Discord bot with moderation, utility, and fun features",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.set_thumbnail(url=self.bot.user.avatar.url if self.bot.user.avatar else self.bot.user.default_avatar.url)

        uptime = int(time.time() - self.start_time)
        days, remainder = divmod(uptime, 86400)
        hours, remainder = divmod(remainder, 3600)
        minutes, seconds = divmod(remainder, 60)
        uptime_str = f"{days}d {hours}h {minutes}m {seconds}s"

        total_members = sum(guild.member_count for guild in self.bot.guilds)

        embed.add_field(name="Developer", value="ModCore Team", inline=True)
        embed.add_field(name="Library", value=f"discord.py {discord.__version__}", inline=True)
        embed.add_field(name="Python Version", value=platform.python_version(), inline=True)

        embed.add_field(name="Servers", value=len(self.bot.guilds), inline=True)
        embed.add_field(name="Users", value=total_members, inline=True)
        embed.add_field(name="Commands", value=len(self.bot.commands), inline=True)

        embed.add_field(name="Uptime", value=uptime_str, inline=True)

        memory = psutil.Process().memory_info().rss / 1024**2
        embed.add_field(name="Memory Usage", value=f"{memory:.2f} MB", inline=True)

        embed.add_field(name="CPU Usage", value=f"{psutil.cpu_percent()}%", inline=True)

        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def help(self, ctx, command=None):
        if command:
            cmd = self.bot.get_command(command)
            if cmd:
                embed = discord.Embed(
                    title=f"Help: {cmd.name}",
                    description=cmd.help or "No description available",
                    color=discord.Color.blue(),
                    timestamp=datetime.datetime.now()
                )

                if cmd.aliases:
                    embed.add_field(name="Aliases", value=", ".join(cmd.aliases), inline=False)

                usage = f"{ctx.prefix}{cmd.name}"
                if cmd.signature:
                    usage += f" {cmd.signature}"
                embed.add_field(name="Usage", value=f"`{usage}`", inline=False)

                embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)
                return
            else:
                embed = discord.Embed(
                    title="Error",
                    description=f"Command `{command}` not found",
                    color=discord.Color.red()
                )
                await ctx.send(embed=embed)
                return

        embed = discord.Embed(
            title="ModCore Help",
            description=f"Use `{ctx.prefix}help <command>` for more information on a command",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        cogs = {}
        for cmd in self.bot.commands:
            cog_name = cmd.cog_name or "No Category"
            if cog_name not in cogs:
                cogs[cog_name] = []
            cogs[cog_name].append(cmd)

        for cog, cmds in sorted(cogs.items()):
            commands_list = ", ".join(f"`{cmd.name}`" for cmd in sorted(cmds, key=lambda x: x.name))
            embed.add_field(name=cog, value=commands_list, inline=False)

        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def afk(self, ctx, *, reason="AFK"):
        user_id = ctx.author.id
        self.afk_users[user_id] = {
            "reason": reason,
            "time": datetime.datetime.now()
        }

        embed = discord.Embed(
            title="AFK Status Set",
            description=f"{ctx.author.mention} is now AFK",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.add_field(name="Reason", value=reason, inline=False)
        embed.set_footer(text=f"AFK since {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        await ctx.send(embed=embed)

        if ctx.author.nick and not ctx.author.nick.startswith("[AFK] "):
            try:
                await ctx.author.edit(nick=f"[AFK] {ctx.author.nick}")
            except:
                pass
        elif not ctx.author.nick:
            try:
                await ctx.author.edit(nick=f"[AFK] {ctx.author.name}")
            except:
                pass

    @commands.Cog.listener()
    async def on_message(self, message):
        if message.author.bot:
            return

        if message.author.id in self.afk_users:
            afk_data = self.afk_users.pop(message.author.id)
            time_afk = datetime.datetime.now() - afk_data["time"]
            hours, remainder = divmod(int(time_afk.total_seconds()), 3600)
            minutes, seconds = divmod(remainder, 60)

            time_str = ""
            if hours > 0:
                time_str += f"{hours}h "
            if minutes > 0:
                time_str += f"{minutes}m "
            time_str += f"{seconds}s"

            embed = discord.Embed(
                title="AFK Status Removed",
                description=f"Welcome back {message.author.mention}! Your AFK status has been removed.",
                color=discord.Color.green(),
                timestamp=datetime.datetime.now()
            )

            embed.add_field(name="AFK Duration", value=time_str, inline=True)
            embed.add_field(name="Reason", value=afk_data["reason"], inline=True)

            await message.channel.send(embed=embed)

            if message.author.nick and message.author.nick.startswith("[AFK] "):
                try:
                    await message.author.edit(nick=message.author.nick[6:])
                except:
                    pass

        mentioned_users = message.mentions
        for user in mentioned_users:
            if user.id in self.afk_users:
                afk_data = self.afk_users[user.id]
                time_afk = datetime.datetime.now() - afk_data["time"]
                hours, remainder = divmod(int(time_afk.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)

                time_str = ""
                if hours > 0:
                    time_str += f"{hours}h "
                if minutes > 0:
                    time_str += f"{minutes}m "
                time_str += f"{seconds}s"

                embed = discord.Embed(
                    title="User is AFK",
                    description=f"{user.mention} is currently AFK",
                    color=discord.Color.gold(),
                    timestamp=datetime.datetime.now()
                )

                embed.add_field(name="Reason", value=afk_data["reason"], inline=True)
                embed.add_field(name="Since", value=time_str + " ago", inline=True)

                await message.channel.send(embed=embed)

    @commands.command()
    async def servericon(self, ctx):
        if not ctx.guild.icon:
            embed = discord.Embed(
                title="Error",
                description="This server doesn't have an icon",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        embed = discord.Embed(
            title=f"{ctx.guild.name} Server Icon",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.set_image(url=ctx.guild.icon.url)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def serverbanner(self, ctx):
        if not ctx.guild.banner:
            embed = discord.Embed(
                title="Error",
                description="This server doesn't have a banner",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        embed = discord.Embed(
            title=f"{ctx.guild.name} Server Banner",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.set_image(url=ctx.guild.banner.url)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def roleinfo(self, ctx, *, role: discord.Role):
        created_at = role.created_at.strftime("%Y-%m-%d %H:%M:%S")
        created_days_ago = (datetime.datetime.now() - role.created_at).days

        embed = discord.Embed(
            title=f"Role Information: {role.name}",
            color=role.color,
            timestamp=datetime.datetime.now()
        )

        embed.add_field(name="ID", value=role.id, inline=True)
        embed.add_field(name="Color", value=str(role.color), inline=True)
        embed.add_field(name="Position", value=role.position, inline=True)

        embed.add_field(name="Mentionable", value="Yes" if role.mentionable else "No", inline=True)
        embed.add_field(name="Hoisted", value="Yes" if role.hoist else "No", inline=True)
        embed.add_field(name="Managed", value="Yes" if role.managed else "No", inline=True)

        permissions = []
        if role.permissions.administrator:
            permissions.append("Administrator")
        if role.permissions.ban_members:
            permissions.append("Ban Members")
        if role.permissions.kick_members:
            permissions.append("Kick Members")
        if role.permissions.manage_channels:
            permissions.append("Manage Channels")
        if role.permissions.manage_guild:
            permissions.append("Manage Server")
        if role.permissions.manage_messages:
            permissions.append("Manage Messages")
        if role.permissions.manage_roles:
            permissions.append("Manage Roles")
        if role.permissions.mention_everyone:
            permissions.append("Mention Everyone")

        if permissions:
            embed.add_field(name="Key Permissions", value=", ".join(permissions), inline=False)

        members_with_role = len(role.members)
        embed.add_field(name="Members", value=members_with_role, inline=True)
        embed.add_field(name="Created", value=f"{created_at}\n{created_days_ago} days ago", inline=True)

        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def channelinfo(self, ctx, *, channel: discord.TextChannel = None):
        channel = channel or ctx.channel

        created_at = channel.created_at.strftime("%Y-%m-%d %H:%M:%S")
        created_days_ago = (datetime.datetime.now() - channel.created_at).days

        embed = discord.Embed(
            title=f"Channel Information: {channel.name}",
            description=channel.topic or "No topic set",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.add_field(name="ID", value=channel.id, inline=True)
        embed.add_field(name="Type", value=str(channel.type).capitalize(), inline=True)
        embed.add_field(name="Category", value=channel.category.name if channel.category else "None", inline=True)

        embed.add_field(name="Position", value=channel.position, inline=True)
        embed.add_field(name="NSFW", value="Yes" if channel.is_nsfw() else "No", inline=True)
        embed.add_field(name="Slowmode", value=f"{channel.slowmode_delay} seconds" if channel.slowmode_delay else "Off", inline=True)

        embed.add_field(name="Created", value=f"{created_at}\n{created_days_ago} days ago", inline=False)

        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def emojis(self, ctx):
        if not ctx.guild.emojis:
            embed = discord.Embed(
                title="Server Emojis",
                description="This server has no custom emojis",
                color=discord.Color.blue(),
                timestamp=datetime.datetime.now()
            )
            await ctx.send(embed=embed)
            return

        animated_emojis = [str(emoji) for emoji in ctx.guild.emojis if emoji.animated]
        static_emojis = [str(emoji) for emoji in ctx.guild.emojis if not emoji.animated]

        embed = discord.Embed(
            title=f"Emojis in {ctx.guild.name}",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        if static_emojis:
            static_chunks = [static_emojis[i:i+20] for i in range(0, len(static_emojis), 20)]
            for i, chunk in enumerate(static_chunks):
                embed.add_field(name=f"Static Emojis {i+1}" if len(static_chunks) > 1 else "Static Emojis", value=" ".join(chunk), inline=False)

        if animated_emojis:
            animated_chunks = [animated_emojis[i:i+20] for i in range(0, len(animated_emojis), 20)]
            for i, chunk in enumerate(animated_chunks):
                embed.add_field(name=f"Animated Emojis {i+1}" if len(animated_chunks) > 1 else "Animated Emojis", value=" ".join(chunk), inline=False)

        embed.set_footer(text=f"Total: {len(ctx.guild.emojis)} | Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def enlarge(self, ctx, emoji: discord.PartialEmoji):
        embed = discord.Embed(
            title=f"Enlarged Emoji: {emoji.name}",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.set_image(url=emoji.url)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

class Fun(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.giveaway_tasks = {}

    @commands.command()
    async def meme(self, ctx):
        async with aiohttp.ClientSession() as session:
            async with session.get('https://www.reddit.com/r/memes/hot.json?limit=100') as response:
                data = await response.json()
                posts = [post['data'] for post in data['data']['children'] if not post['data']['stickied'] and post['data']['url'].endswith(('jpg', 'jpeg', 'png', 'gif'))]

                if not posts:
                    await ctx.send("Couldn't find any memes!")
                    return

                post = random.choice(posts)

                embed = discord.Embed(
                    title=post['title'],
                    url=f"https://reddit.com{post['permalink']}",
                    color=discord.Color.random(),
                    timestamp=datetime.datetime.now()
                )

                embed.set_image(url=post['url'])
                embed.set_footer(text=f"👍 {post['ups']} | 💬 {post['num_comments']} | Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def joke(self, ctx):
        async with aiohttp.ClientSession() as session:
            async with session.get('https://official-joke-api.appspot.com/random_joke') as response:
                data = await response.json()

                embed = discord.Embed(
                    title="Joke",
                    description=f"**{data['setup']}**\n\n{data['punchline']}",
                    color=discord.Color.random(),
                    timestamp=datetime.datetime.now()
                )

                embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def coinflip(self, ctx):
        result = random.choice(["Heads", "Tails"])

        embed = discord.Embed(
            title="Coin Flip",
            description=f"The coin landed on **{result}**!",
            color=discord.Color.gold(),
            timestamp=datetime.datetime.now()
        )

        embed.set_thumbnail(url="https://i.imgur.com/7tL4CQj.png" if result == "Heads" else "https://i.imgur.com/jShTYAY.png")
        embed.set_footer(text=f"Flipped by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def roll(self, ctx, dice: str = "1d6"):
        try:
            rolls, limit = map(int, dice.split('d'))
        except ValueError:
            embed = discord.Embed(
                title="Error",
                description="Format has to be in NdN (e.g. 1d6, 2d20)",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        if rolls > 100 or limit > 1000:
            embed = discord.Embed(
                title="Error",
                description="Too many dice or too high limit. Maximum is 100d1000",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        results = [random.randint(1, limit) for _ in range(rolls)]
        total = sum(results)

        embed = discord.Embed(
            title=f"Dice Roll: {dice}",
            description=f"Total: **{total}**",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        if rolls > 1:
            embed.add_field(name="Individual Rolls", value=", ".join(str(r) for r in results), inline=False)

        embed.set_footer(text=f"Rolled by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def eightball(self, ctx, *, question):
        responses = [
            "It is certain.",
            "It is decidedly so.",
            "Without a doubt.",
            "Yes - definitely.",
            "You may rely on it.",
            "As I see it, yes.",
            "Most likely.",
            "Outlook good.",
            "Yes.",
            "Signs point to yes.",
            "Reply hazy, try again.",
            "Ask again later.",
            "Better not tell you now.",
            "Cannot predict now.",
            "Concentrate and ask again.",
            "Don't count on it.",
            "My reply is no.",
            "My sources say no.",
            "Outlook not so good.",
            "Very doubtful."
        ]

        response = random.choice(responses)

        embed = discord.Embed(
            title="🎱 Magic 8-Ball",
            color=discord.Color.purple(),
            timestamp=datetime.datetime.now()
        )

        embed.add_field(name="Question", value=question, inline=False)
        embed.add_field(name="Answer", value=response, inline=False)
        embed.set_footer(text=f"Asked by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def rps(self, ctx, choice=None):
        choices = ["rock", "paper", "scissors"]

        if choice is None or choice.lower() not in choices:
            embed = discord.Embed(
                title="Error",
                description="Please choose rock, paper, or scissors",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        user_choice = choice.lower()
        bot_choice = random.choice(choices)

        emojis = {
            "rock": "🪨",
            "paper": "📄",
            "scissors": "✂️"
        }

        result = ""
        if user_choice == bot_choice:
            result = "It's a tie!"
            color = discord.Color.gold()
        elif (user_choice == "rock" and bot_choice == "scissors") or \
             (user_choice == "paper" and bot_choice == "rock") or \
             (user_choice == "scissors" and bot_choice == "paper"):
            result = "You win!"
            color = discord.Color.green()
        else:
            result = "I win!"
            color = discord.Color.red()

        embed = discord.Embed(
            title="Rock Paper Scissors",
            description=result,
            color=color,
            timestamp=datetime.datetime.now()
        )

        embed.add_field(name="Your Choice", value=f"{emojis[user_choice]} {user_choice.capitalize()}", inline=True)
        embed.add_field(name="My Choice", value=f"{emojis[bot_choice]} {bot_choice.capitalize()}", inline=True)
        embed.set_footer(text=f"Played by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def cat(self, ctx):
        async with aiohttp.ClientSession() as session:
            async with session.get('https://api.thecatapi.com/v1/images/search') as response:
                data = await response.json()

                embed = discord.Embed(
                    title="Random Cat",
                    color=discord.Color.random(),
                    timestamp=datetime.datetime.now()
                )

                embed.set_image(url=data[0]['url'])
                embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def dog(self, ctx):
        async with aiohttp.ClientSession() as session:
            async with session.get('https://api.thedogapi.com/v1/images/search') as response:
                data = await response.json()

                embed = discord.Embed(
                    title="Random Dog",
                    color=discord.Color.random(),
                    timestamp=datetime.datetime.now()
                )

                embed.set_image(url=data[0]['url'])
                embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def fact(self, ctx):
        async with aiohttp.ClientSession() as session:
            async with session.get('https://uselessfacts.jsph.pl/random.json?language=en') as response:
                data = await response.json()

                embed = discord.Embed(
                    title="Random Fact",
                    description=data['text'],
                    color=discord.Color.blue(),
                    timestamp=datetime.datetime.now()
                )

                embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def quote(self, ctx):
        async with aiohttp.ClientSession() as session:
            async with session.get('https://api.quotable.io/random') as response:
                data = await response.json()

                embed = discord.Embed(
                    title="Random Quote",
                    description=f"*\"{data['content']}\"*",
                    color=discord.Color.blue(),
                    timestamp=datetime.datetime.now()
                )

                embed.set_footer(text=f"— {data['author']} | Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def poll(self, ctx, question, *options):
        if len(options) < 2:
            embed = discord.Embed(
                title="Error",
                description="Please provide at least 2 options",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        if len(options) > 10:
            embed = discord.Embed(
                title="Error",
                description="You can only have up to 10 options",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        reactions = ["1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣", "7️⃣", "8️⃣", "9️⃣", "🔟"]

        description = []
        for i, option in enumerate(options):
            description.append(f"{reactions[i]} {option}")

        embed = discord.Embed(
            title=question,
            description="\n".join(description),
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.set_footer(text=f"Poll by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        poll_msg = await ctx.send(embed=embed)

        for i in range(len(options)):
            await poll_msg.add_reaction(reactions[i])

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def giveaway(self, ctx, duration: str, winners: int, *, prize):
        if winners < 1:
            embed = discord.Embed(
                title="Error",
                description="Number of winners must be at least 1",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        time_units = {"s": 1, "m": 60, "h": 3600, "d": 86400}
        time_value = int(''.join(filter(str.isdigit, duration)))
        time_unit = ''.join(filter(str.isalpha, duration)).lower()

        if time_unit not in time_units:
            embed = discord.Embed(
                title="Error",
                description="Invalid time unit. Use s (seconds), m (minutes), h (hours), or d (days)",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        seconds = time_value * time_units[time_unit]
        end_time = datetime.datetime.now() + datetime.timedelta(seconds=seconds)

        embed = discord.Embed(
            title="🎉 GIVEAWAY 🎉",
            description=f"**{prize}**",
            color=0x00FF00,
            timestamp=datetime.datetime.now()
        )

        embed.add_field(name="Hosted by", value=ctx.author.mention, inline=True)
        embed.add_field(name="Winners", value=str(winners), inline=True)
        embed.add_field(name="Ends at", value=f"<t:{int(end_time.timestamp())}:R>", inline=True)
        embed.set_footer(text="React with 🎉 to enter!")

        message = await ctx.send(embed=embed)
        await message.add_reaction("🎉")

        if ctx.guild.id in self.giveaway_tasks:
            self.giveaway_tasks[ctx.guild.id].append((message.id, asyncio.create_task(self.end_giveaway(seconds, message.id, message.channel.id, winners, prize, ctx.author.id))))
        else:
            self.giveaway_tasks[ctx.guild.id] = [(message.id, asyncio.create_task(self.end_giveaway(seconds, message.id, message.channel.id, winners, prize, ctx.author.id)))]

    async def end_giveaway(self, seconds, message_id, channel_id, winners_count, prize, host_id):
        await asyncio.sleep(seconds)

        channel = self.bot.get_channel(channel_id)
        if not channel:
            return

        message = await channel.fetch_message(message_id)
        if not message:
            return

        reaction = discord.utils.get(message.reactions, emoji="🎉")
        if not reaction:
            return

        users = await reaction.users().flatten()
        users = [user for user in users if not user.bot]

        if len(users) < winners_count:
            winners_count = len(users)

        if winners_count == 0:
            embed = discord.Embed(
                title="Giveaway Ended",
                description=f"**{prize}**\n\nNo valid entries!",
                color=0xFF0000,
                timestamp=datetime.datetime.now()
            )

            host = self.bot.get_user(host_id)
            embed.add_field(name="Hosted by", value=host.mention if host else "Unknown", inline=True)

            await message.edit(embed=embed)
            return

        winners = random.sample(users, winners_count)
        winners_mentions = [winner.mention for winner in winners]

        embed = discord.Embed(
            title="Giveaway Ended",
            description=f"**{prize}**",
            color=0xFF0000,
            timestamp=datetime.datetime.now()
        )

        host = self.bot.get_user(host_id)
        embed.add_field(name="Hosted by", value=host.mention if host else "Unknown", inline=True)
        embed.add_field(name="Winners", value="\n".join(winners_mentions), inline=True)

        await message.edit(embed=embed)

        winners_announcement = f"🎉 Congratulations {', '.join(winners_mentions)}! You won **{prize}**!"
        await channel.send(winners_announcement)

        for guild_id, tasks in self.giveaway_tasks.items():
            for i, (msg_id, task) in enumerate(tasks):
                if msg_id == message_id:
                    self.giveaway_tasks[guild_id].pop(i)
                    break

    @commands.command()
    async def remind(self, ctx, duration: str, *, reminder):
        time_units = {"s": 1, "m": 60, "h": 3600, "d": 86400}
        time_value = int(''.join(filter(str.isdigit, duration)))
        time_unit = ''.join(filter(str.isalpha, duration)).lower()

        if time_unit not in time_units:
            embed = discord.Embed(
                title="Error",
                description="Invalid time unit. Use s (seconds), m (minutes), h (hours), or d (days)",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        seconds = time_value * time_units[time_unit]
        end_time = datetime.datetime.now() + datetime.timedelta(seconds=seconds)

        embed = discord.Embed(
            title="Reminder Set",
            description=f"I'll remind you about: **{reminder}**",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.add_field(name="When", value=f"<t:{int(end_time.timestamp())}:R>", inline=True)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

        await asyncio.sleep(seconds)

        reminder_embed = discord.Embed(
            title="Reminder",
            description=f"You asked me to remind you about: **{reminder}**",
            color=discord.Color.green(),
            timestamp=datetime.datetime.now()
        )

        reminder_embed.add_field(name="Original Reminder Time", value=f"<t:{int(end_time.timestamp())}:F>", inline=True)
        reminder_embed.set_footer(text=f"Reminder for {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        try:
            await ctx.author.send(embed=reminder_embed)
        except:
            await ctx.send(f"{ctx.author.mention}", embed=reminder_embed)

    @commands.command()
    async def weather(self, ctx, *, location):
        api_key = "YOUR_OPENWEATHER_API_KEY"
        base_url = "http://api.openweathermap.org/data/2.5/weather?"

        complete_url = f"{base_url}q={location}&appid={api_key}&units=metric"

        async with aiohttp.ClientSession() as session:
            async with session.get(complete_url) as response:
                if response.status != 200:
                    embed = discord.Embed(
                        title="Error",
                        description="Location not found",
                        color=discord.Color.red()
                    )
                    await ctx.send(embed=embed)
                    return

                data = await response.json()

                main = data["main"]
                weather = data["weather"][0]
                wind = data["wind"]
                sys = data["sys"]

                temp = main["temp"]
                feels_like = main["feels_like"]
                humidity = main["humidity"]
                pressure = main["pressure"]

                weather_desc = weather["description"]
                weather_icon = weather["icon"]

                wind_speed = wind["speed"]

                country = sys["country"]
                sunrise = datetime.datetime.fromtimestamp(sys["sunrise"]).strftime("%H:%M")
                sunset = datetime.datetime.fromtimestamp(sys["sunset"]).strftime("%H:%M")

                embed = discord.Embed(
                    title=f"Weather in {data['name']}, {country}",
                    description=f"**{weather_desc.capitalize()}**",
                    color=discord.Color.blue(),
                    timestamp=datetime.datetime.now()
                )

                embed.set_thumbnail(url=f"http://openweathermap.org/img/wn/{weather_icon}@2x.png")

                embed.add_field(name="Temperature", value=f"{temp}°C / {round(temp * 9/5 + 32, 1)}°F", inline=True)
                embed.add_field(name="Feels Like", value=f"{feels_like}°C / {round(feels_like * 9/5 + 32, 1)}°F", inline=True)
                embed.add_field(name="Humidity", value=f"{humidity}%", inline=True)

                embed.add_field(name="Wind Speed", value=f"{wind_speed} m/s", inline=True)
                embed.add_field(name="Pressure", value=f"{pressure} hPa", inline=True)
                embed.add_field(name="☀️ / 🌙", value=f"Sunrise: {sunrise}\nSunset: {sunset}", inline=True)

                embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def translate(self, ctx, target_lang, *, text):
        languages = {
            "af": "Afrikaans", "sq": "Albanian", "am": "Amharic", "ar": "Arabic", "hy": "Armenian",
            "az": "Azerbaijani", "eu": "Basque", "be": "Belarusian", "bn": "Bengali", "bs": "Bosnian",
            "bg": "Bulgarian", "ca": "Catalan", "ceb": "Cebuano", "zh": "Chinese", "co": "Corsican",
            "hr": "Croatian", "cs": "Czech", "da": "Danish", "nl": "Dutch", "en": "English",
            "eo": "Esperanto", "et": "Estonian", "fi": "Finnish", "fr": "French", "fy": "Frisian",
            "gl": "Galician", "ka": "Georgian", "de": "German", "el": "Greek", "gu": "Gujarati",
            "ht": "Haitian Creole", "ha": "Hausa", "haw": "Hawaiian", "he": "Hebrew", "hi": "Hindi",
            "hmn": "Hmong", "hu": "Hungarian", "is": "Icelandic", "ig": "Igbo", "id": "Indonesian",
            "ga": "Irish", "it": "Italian", "ja": "Japanese", "jv": "Javanese", "kn": "Kannada",
            "kk": "Kazakh", "km": "Khmer", "rw": "Kinyarwanda", "ko": "Korean", "ku": "Kurdish",
            "ky": "Kyrgyz", "lo": "Lao", "la": "Latin", "lv": "Latvian", "lt": "Lithuanian",
            "lb": "Luxembourgish", "mk": "Macedonian", "mg": "Malagasy", "ms": "Malay", "ml": "Malayalam",
            "mt": "Maltese", "mi": "Maori", "mr": "Marathi", "mn": "Mongolian", "my": "Myanmar",
            "ne": "Nepali", "no": "Norwegian", "ny": "Nyanja", "or": "Odia", "ps": "Pashto",
            "fa": "Persian", "pl": "Polish", "pt": "Portuguese", "pa": "Punjabi", "ro": "Romanian",
            "ru": "Russian", "sm": "Samoan", "gd": "Scots Gaelic", "sr": "Serbian", "st": "Sesotho",
            "sn": "Shona", "sd": "Sindhi", "si": "Sinhala", "sk": "Slovak", "sl": "Slovenian",
            "so": "Somali", "es": "Spanish", "su": "Sundanese", "sw": "Swahili", "sv": "Swedish",
            "tl": "Tagalog", "tg": "Tajik", "ta": "Tamil", "tt": "Tatar", "te": "Telugu", "th": "Thai",
            "tr": "Turkish", "tk": "Turkmen", "uk": "Ukrainian", "ur": "Urdu", "ug": "Uyghur",
            "uz": "Uzbek", "vi": "Vietnamese", "cy": "Welsh", "xh": "Xhosa", "yi": "Yiddish",
            "yo": "Yoruba", "zu": "Zulu"
        }

        if target_lang not in languages:
            embed = discord.Embed(
                title="Error",
                description="Invalid language code. Use a valid ISO 639-1 language code.",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        url = "https://translate.googleapis.com/translate_a/single"
        params = {
            "client": "gtx",
            "sl": "auto",
            "tl": target_lang,
            "dt": "t",
            "q": text
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    embed = discord.Embed(
                        title="Error",
                        description="Failed to translate text",
                        color=discord.Color.red()
                    )
                    await ctx.send(embed=embed)
                    return

                data = await response.json()

                translated_text = ""
                for sentence in data[0]:
                    if sentence[0]:
                        translated_text += sentence[0]

                detected_lang_code = data[2]
                detected_lang = languages.get(detected_lang_code, detected_lang_code)
                target_lang_name = languages.get(target_lang, target_lang)

                embed = discord.Embed(
                    title="Translation",
                    color=discord.Color.blue(),
                    timestamp=datetime.datetime.now()
                )

                embed.add_field(name=f"Original ({detected_lang})", value=text, inline=False)
                embed.add_field(name=f"Translation ({target_lang_name})", value=translated_text, inline=False)
                embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def urban(self, ctx, *, term):
        url = f"https://api.urbandictionary.com/v0/define?term={term}"

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status != 200:
                    embed = discord.Embed(
                        title="Error",
                        description="Failed to fetch definition",
                        color=discord.Color.red()
                    )
                    await ctx.send(embed=embed)
                    return

                data = await response.json()

                if not data["list"]:
                    embed = discord.Embed(
                        title="Error",
                        description=f"No definitions found for '{term}'",
                        color=discord.Color.red()
                    )
                    await ctx.send(embed=embed)
                    return

                definition = data["list"][0]

                def clean_text(text):
                    return text.replace("[", "").replace("]", "")

                embed = discord.Embed(
                    title=f"Urban Dictionary: {definition['word']}",
                    url=definition["permalink"],
                    color=discord.Color.blue(),
                    timestamp=datetime.datetime.now()
                )

                definition_text = clean_text(definition["definition"])
                if len(definition_text) > 1024:
                    definition_text = definition_text[:1021] + "..."

                example_text = clean_text(definition["example"])
                if len(example_text) > 1024:
                    example_text = example_text[:1021] + "..."

                embed.add_field(name="Definition", value=definition_text, inline=False)

                if example_text:
                    embed.add_field(name="Example", value=example_text, inline=False)

                embed.add_field(name="👍", value=definition["thumbs_up"], inline=True)
                embed.add_field(name="👎", value=definition["thumbs_down"], inline=True)

                embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

                await ctx.send(embed=embed)

    @commands.command()
    async def qrcode(self, ctx, *, content):
        if len(content) > 1000:
            embed = discord.Embed(
                title="Error",
                description="Content too long. Maximum length is 1000 characters.",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        qr_url = f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={content}"

        embed = discord.Embed(
            title="QR Code",
            description=f"Content: {content[:100]}{'...' if len(content) > 100 else ''}",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.set_image(url=qr_url)
        embed.set_footer(text=f"Requested by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        await ctx.send(embed=embed)

    @commands.command()
    async def poll_advanced(self, ctx, title, *options):
        if len(options) < 2 or len(options) > 20:
            embed = discord.Embed(
                title="Error",
                description="Please provide between 2 and 20 options",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
            return

        alphabet = "🇦🇧🇨🇩🇪🇫🇬🇭🇮🇯🇰🇱🇲🇳🇴🇵🇶🇷🇸🇹🇺🇻🇼🇽🇾🇿"
        reactions = [alphabet[i] for i in range(len(options))]

        description = []
        for i, option in enumerate(options):
            description.append(f"{reactions[i]} {option}")

        embed = discord.Embed(
            title=title,
            description="\n\n".join(description),
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.set_footer(text=f"Poll by {ctx.author}", icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url)

        poll_msg = await ctx.send(embed=embed)

        for i in range(len(options)):
            await poll_msg.add_reaction(reactions[i])

@tree.command(name="userinfo", description="Get information about a user")
async def userinfo(interaction: discord.Interaction, member: discord.Member = None):
    member = member or interaction.user

    roles = [role.mention for role in member.roles if role.name != "@everyone"]
    roles_str = ", ".join(roles) if roles else "None"

    joined_at = member.joined_at.strftime("%Y-%m-%d %H:%M:%S")
    joined_days_ago = (datetime.datetime.now() - member.joined_at).days

    created_at = member.created_at.strftime("%Y-%m-%d %H:%M:%S")
    created_days_ago = (datetime.datetime.now() - member.created_at).days

    embed = discord.Embed(
        title=f"{member} User Information",
        color=member.color,
        timestamp=datetime.datetime.now()
    )

    embed.set_thumbnail(url=member.avatar.url if member.avatar else member.default_avatar.url)

    embed.add_field(name="ID", value=member.id, inline=True)
    embed.add_field(name="Nickname", value=member.nick or "None", inline=True)
    embed.add_field(name="Status", value=str(member.status).capitalize(), inline=True)

    embed.add_field(name="Joined Server", value=f"{joined_at}\n{joined_days_ago} days ago", inline=True)
    embed.add_field(name="Account Created", value=f"{created_at}\n{created_days_ago} days ago", inline=True)

    if member.activity:
        activity_type = str(member.activity.type).split('.')[-1].capitalize()
        activity_name = member.activity.name
        embed.add_field(name="Activity", value=f"{activity_type}: {activity_name}", inline=False)

    if roles:
        embed.add_field(name=f"Roles [{len(roles)}]", value=roles_str, inline=False)

    embed.set_footer(text=f"Requested by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)

    await interaction.response.send_message(embed=embed)

@tree.command(name="avatar", description="Get a user's avatar")
async def avatar(interaction: discord.Interaction, member: discord.Member = None):
    member = member or interaction.user

    embed = discord.Embed(
        title=f"{member}'s Avatar",
        color=discord.Color.blue(),
        timestamp=datetime.datetime.now()
    )

    avatar_url = member.avatar.url if member.avatar else member.default_avatar.url
    embed.set_image(url=avatar_url)
    embed.set_footer(text=f"Requested by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)

    await interaction.response.send_message(embed=embed)

@tree.command(name="meme", description="Get a random meme from Reddit")
async def meme(interaction: discord.Interaction):
    await interaction.response.defer()

    async with aiohttp.ClientSession() as session:
        async with session.get('https://www.reddit.com/r/memes/hot.json?limit=100') as response:
            data = await response.json()
            posts = [post['data'] for post in data['data']['children'] if not post['data']['stickied'] and post['data']['url'].endswith(('jpg', 'jpeg', 'png', 'gif'))]

            if not posts:
                await interaction.followup.send("Couldn't find any memes!")
                return

            post = random.choice(posts)

            embed = discord.Embed(
                title=post['title'],
                url=f"https://reddit.com{post['permalink']}",
                color=discord.Color.random(),
                timestamp=datetime.datetime.now()
            )

            embed.set_image(url=post['url'])
            embed.set_footer(text=f"👍 {post['ups']} | 💬 {post['num_comments']} | Requested by {interaction.user}", icon_url=interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url)

            await interaction.followup.send(embed=embed)

@client.event
async def on_message(message):
    if message.author == client.user:
        return

async def main():
    await client.start('')

if __name__ == "__main__":
    asyncio.run(main())